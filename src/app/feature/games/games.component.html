<section>
  <div class="wrapper">
    <app-breadcrumb [data]="[{ label: 'J<PERSON>tékok' }]" />
    <app-page-title [title]="'Bors Játékok'" />
  </div>
</section>
<div class="content-wrapper">
  <section class="games-content">
    <div class="wrapper">
      <div class="games-head"></div>
      <div class="related-game">
        <kesma-advertisement-adocean [ad]="mainGame" [hasNoParentHeight]="true" class="jatek-elem"></kesma-advertisement-adocean>
      </div>
      <div class="games-lb">
        <div class="goaWrap">
          <kesma-advertisement-adocean [ad]="gameAdDesktop" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
          <kesma-advertisement-adocean [ad]="gameAdMobile" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
        </div>
      </div>
      @if (canShowGames()) {
        <div class="list-of-games">
          @for (game of games; track game.masterId) {
            <kesma-advertisement-adocean [ad]="game" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
          }
        </div>
      }
    </div>
  </section>
</div>
