import { Component, inject, OnInit } from '@angular/core';
import { LexiconMetaService } from '../../../api/lexicon-meta.service';
import { toSignal } from '@angular/core/rxjs-interop';
import { IStarCard, StarCardTypes } from '../../definitions/star.definitions';
import { LexiconBreadcrumbComponent } from '../../../components/lexicon-breadcrumb/lexicon-breadcrumb.component';
import { StarCardComponent } from '../star-card/star-card.component';
import { StarWrapperComponent } from '../star-wrapper/star-wrapper.component';
import { StarSearchFilterComponent } from './components/star-search-filter/star-search-filter.component';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { map } from 'rxjs/operators';
import { PagerComponent } from '../../../../../shared';
import { AsyncPipe } from '@angular/common';
import { AdvertisementAdoceanComponent, AdvertisementAdoceanStoreService, ApiListResult, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { SeoService } from '@trendency/kesma-core';

@Component({
  selector: 'app-star-search-page',
  imports: [
    LexiconBreadcrumbComponent,
    StarCardComponent,
    StarWrapperComponent,
    StarSearchFilterComponent,
    RouterLink,
    PagerComponent,
    AsyncPipe,
    AdvertisementAdoceanComponent,
  ],
  templateUrl: './star-search-page.component.html',
  styleUrl: './star-search-page.component.scss',
})
export class StarSearchPageComponent implements OnInit {
  private readonly breadcrumbService = inject(LexiconMetaService);
  private readonly route = inject(ActivatedRoute);
  private readonly adStore = inject(AdvertisementAdoceanStoreService);
  private readonly seoService = inject(SeoService);

  readonly breadcrumbItems = toSignal(this.breadcrumbService.getStarPageBreadcrumbs());
  readonly ads = toSignal(this.adStore.advertisemenets$.pipe(map((ads) => this.adStore.separateAdsByMedium(ads))));

  readonly StarCardTypes = StarCardTypes;

  readonly query$ = this.route.data.pipe(map(({ data }) => data?.query as string | undefined));
  readonly response$ = this.route.data.pipe(map(({ data }) => data?.response as ApiListResult<IStarCard>));
  readonly data$ = this.response$.pipe(map(({ data }) => data));
  readonly meta$ = this.response$.pipe(map(({ meta }) => meta));
  readonly hasPages$ = this.meta$.pipe(map(({ limitable }) => (limitable?.rowAllCount ?? 0) > (limitable?.rowOnPageCount ?? 0)));

  ngOnInit(): void {
    const canonical = createCanonicalUrlForPageablePage('lexikon/sztar/kereso');
    this.seoService.updateCanonicalUrl(canonical);
  }
}
