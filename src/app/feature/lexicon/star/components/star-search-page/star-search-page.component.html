<app-star-wrapper [leftContent]="leftContent"></app-star-wrapper>

<ng-template #leftContent>
  <div class="search">
    <app-lexicon-breadcrumb [items]="breadcrumbItems() ?? []" />
    <section class="search-tabs">
      <a class="search-tabs-link active" [routerLink]="[]">SZTÁRKERESŐ</a>
      <a class="search-tabs-link" [routerLink]="['..', 'abc', 'a']">SZTÁR ABC</a>
    </section>
    <app-star-search-filter />
    <p class="search-result-count">
      @if (query$ | async; as name) {
        <span class="search-result-count-query">'{{ name }}'</span> kifejezésre
      }
      {{ (meta$ | async)?.limitable?.rowAllCount ?? 0 }} találat
    </p>
    <section class="search-result-list">
      @for (star of data$ | async; track star.id) {
        <app-star-card [data]="star" [styleId]="$first ? StarCardTypes.FEATURED : StarCardTypes.LIST"></app-star-card>

        @if ($index === 3) {
          @if (ads()?.desktop?.roadblock_1; as ad) {
            <kesma-advertisement-adocean [style]="{ margin: 'var(--kesma-advert-margin)' }" [ad]="ad" />
          }
          @if (ads()?.mobile?.mobilrectangle_1; as ad) {
            <kesma-advertisement-adocean [style]="{ margin: 'var(--kesma-advert-margin)' }" [ad]="ad" />
          }
        }
        @if ($index === 7) {
          @if (ads()?.desktop?.roadblock_2; as ad) {
            <kesma-advertisement-adocean [style]="{ margin: 'var(--kesma-advert-margin)' }" [ad]="ad" />
          }
          @if (ads()?.mobile?.mobilrectangle_2; as ad) {
            <kesma-advertisement-adocean [style]="{ margin: 'var(--kesma-advert-margin)' }" [ad]="ad" />
          }
        }
      }

      @if (hasPages$ | async) {
        <app-pager
          [rowAllCount]="(meta$ | async)?.limitable?.rowAllCount!"
          [rowOnPageCount]="(meta$ | async)?.limitable?.rowOnPageCount!"
          [isListPager]="true"
          [hasFirstLastButton]="false"
          [hasSkipButton]="true"
          [maxDisplayedPages]="5"
        />
      }

      @if (ads()?.mobile?.mobilrectangle_3; as ad) {
        <kesma-advertisement-adocean [style]="{ margin: 'var(--kesma-advert-margin)' }" [ad]="ad" />
      }

      @if (ads()?.desktop?.roadblock_3; as ad) {
        <kesma-advertisement-adocean [style]="{ margin: 'var(--kesma-advert-margin)' }" [ad]="ad" />
      }
    </section>
  </div>
</ng-template>
