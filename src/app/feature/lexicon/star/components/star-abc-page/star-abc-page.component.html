<app-star-wrapper [leftContent]="leftContent" [isAbcSearchPage]="true"></app-star-wrapper>

<ng-template #leftContent>
  <app-lexicon-breadcrumb [items]="breadcrumbItems$ | async"></app-lexicon-breadcrumb>
  <section class="search-tabs">
    <a class="search-tabs-link" [routerLink]="['/', 'lexikon', 'sztar', 'kereso']">SZTÁRKERESŐ</a>
    <a class="search-tabs-link active" [routerLink]="[]">SZTÁR ABC</a>
  </section>

  <app-star-abc-filter [starAbc]="starAbc" [activeLetter]="activeLetter"></app-star-abc-filter>

  <div class="results">{{ limitable?.rowAllCount ?? 0 }} találat</div>

  <app-pager
    *ngIf="limitable?.pageMax > 0"
    [hasSkipButton]="true"
    [isListPager]="true"
    [maxDisplayedPages]="5"
    [rowAllCount]="limitable?.rowAllCount"
    [rowOnPageCount]="limitable?.rowOnPageCount"
  >
  </app-pager>

  <div class="active-letter-separator">{{ activeLetter }}</div>

  <section class="star-cards">
    @for (star of stars; track star.id) {
      <app-star-card [data]="star" [styleId]="StarCardTypes.SIMPLE"></app-star-card>

      @if ($index === 7) {
        <div class="advert">
          <!-- ADVERTISEMENT -->
          <kesma-advertisement-adocean [style]="{ margin: 'var(--kesma-advert-margin)' }" [hasNoParentHeight]="true" *ngIf="ads()?.desktop?.roadblock_1 as ad" [ad]="ad"></kesma-advertisement-adocean>
          <kesma-advertisement-adocean [style]="{ margin: 'var(--kesma-advert-margin)' }" [hasNoParentHeight]="true" *ngIf="ads()?.mobile?.mobilrectangle_1 as ad" [ad]="ad"></kesma-advertisement-adocean>
        </div>
      }

      @if ($index === 15) {
        <div class="advert">
          @if (ads()?.mobile?.mobilrectangle_2; as ad) {
            <kesma-advertisement-adocean [style]="{ margin: 'var(--kesma-advert-margin)' }" [ad]="ad" class="mobile" />
          }

          @if (ads()?.desktop?.roadblock_2; as ad) {
            <kesma-advertisement-adocean [style]="{ margin: 'var(--kesma-advert-margin)' }" [ad]="ad" class="desktop" />
          }
        </div>
      }
    }
  </section>

  <app-pager
    *ngIf="limitable?.pageMax > 0"
    [hasSkipButton]="true"
    [isListPager]="true"
    [maxDisplayedPages]="5"
    [rowAllCount]="limitable?.rowAllCount"
    [rowOnPageCount]="limitable?.rowOnPageCount"
    class="pager-page-end"
  ></app-pager>

  <app-star-abc-filter class="filter-page-end" [starAbc]="starAbc" [activeLetter]="activeLetter"></app-star-abc-filter>

  @if (ads()?.mobile?.mobilrectangle_3; as ad) {
    <kesma-advertisement-adocean [style]="{ margin: 'var(--kesma-advert-margin)' }" [ad]="ad" class="mobile" />
  }

  @if (ads()?.desktop?.roadblock_3; as ad) {
    <kesma-advertisement-adocean [style]="{ margin: 'var(--kesma-advert-margin)' }" [ad]="ad" class="desktop" />
  }
</ng-template>
