@if (!isUserAdultChoice() && article()?.isAdultsOnly) {
  <app-adult-layer (isUserAdult)="onIsUserAdultChoose($event)"></app-adult-layer>
} @else {
  <section class="article">
    <div class="wrapper column">
      <app-breadcrumb [data]="breadcrumbItems()" />
      @if (article()?.videoLead?.video; as video) {
        <kesma-floating-video>
          <kesma-article-video [data]="video"></kesma-article-video>
        </kesma-floating-video>
      } @else if (article().thumbnail && !article().hideThumbnailFromBody) {
        <img
          withFocusPoint
          class="thumbnail"
          [data]="article()?.thumbnailFocusedImages"
          [displayedAspectRatio]="{ desktop: '16:9' }"
          [displayedUrl]="article().thumbnail"
          [alt]="article()?.thumbnailInfo?.altText || article().title"
          fetchpriority="high"
        />
      }
      <h1 class="title">{{ article().title }}</h1>
      @if (article()?.tags?.[0]; as firstTag) {
        <a class="first-tag" [routerLink]="['/cimke', firstTag.slug]">
          {{ firstTag.title }}
        </a>
      }
      <div class="dates">
        <strong>PUBLIKÁLÁS:</strong> {{ article().publishDate | dfnsFormat: 'yyyy. LLLL dd. HH:mm' }}
        @if (article()?.lastUpdated) {
          / <strong>FRISSÍTÉS:</strong>
          {{ article().lastUpdated | dfnsFormat: 'yyyy. LLLL dd. HH:mm' }}
        }
      </div>
      @if (adverts?.desktop?.leaderboard_1; as ad) {
        <kesma-advertisement-adocean [style]="{ margin: '32px 0px' }" [isExceptionAdvertEnabled]="isExceptionAdvertEnabled" [ad]="ad"></kesma-advertisement-adocean>
      }
      @if (article()?.tags?.length > 1) {
        <div class="tags-wrapper">
          @for (tag of article().tags | slice: 1; track tag.slug) {
            <a class="tag" [routerLink]="['/cimke', tag.slug]">{{ tag.title }}</a>
          }
        </div>
      }
    </div>
    @if (isMinuteByMinute && minuteToMinuteBlocks()?.length) {
      <div class="wrapper">
        <app-minute-by-minute-timeline [data]="minuteToMinuteBlocks()" />
      </div>
    }
    <div class="wrapper with-aside">
      <div class="left-column column">
        <div class="lead block">{{ article().lead }}</div>

        <div class="block share">
          <app-social-share [emailSubject]="article()?.title || ''"></app-social-share>
          <div class="comment-and-bookmark">
            <a class="comment-count" routerLink="." fragment="comment-section">
              <kesma-icon name="comment" [size]="16" />
              KOMMENT ({{ articleSocials()?.commentCount ?? 0 }})
            </a>
            <div class="bookmark">
              @if (isBookmarked()) {
                <kesma-icon class="active" name="bookmark-active" [size]="16" (click)="handleBookmarkClick('delete')" />
              } @else {
                <kesma-icon name="bookmark" [size]="16" (click)="handleBookmarkClick('save')" />
              }
            </div>
          </div>
        </div>

        @if (article()?.publicAuthor; as authorName) {
          <div class="author-data block">
            <div class="author">
              @if (article()?.publicAuthorSlug; as slug) {
                <a [routerLink]="['/', 'szerzo', slug]">{{ authorName }}</a>
              } @else {
                {{ authorName }}
              }

              @if (article()?.publicAuthorTitle; as rank) {
                <div class="author-rank">{{ rank }}</div>
              }
            </div>
            @if (article()?.publicAuthorSlug; as slug) {
              <a class="author-link" [routerLink]="['/szerzo', slug]">
                A szerző cikkei
                <kesma-icon class="arrow-right" name="arrow-right-long" size="14" height="16"></kesma-icon>
              </a>
            }
          </div>
        }

        @if (adverts?.desktop?.roadblock_1; as ad) {
          <kesma-advertisement-adocean [style]="{ margin: '32px 0px' }" [isExceptionAdvertEnabled]="isExceptionAdvertEnabled" [ad]="ad"></kesma-advertisement-adocean>
        }
        @if (adverts?.mobile?.mobilrectangle_1; as ad) {
          <kesma-advertisement-adocean [style]="{ margin: '32px 0px' }" [isExceptionAdvertEnabled]="isExceptionAdvertEnabled" [ad]="ad"></kesma-advertisement-adocean>
        }

        @if (isMinuteByMinute) {
          <div class="mbm-header block">
            <strong class="mbm-header-text">Cikkünk folyamatosan frissül</strong>
            <div class="mbm-header-count">{{ article().minuteToMinuteBlocks?.length }} bejegyzés</div>
          </div>

          @for (post of article().minuteToMinuteBlocks; track post.id) {
            <div class="scroll-target-wrapper">
              <div class="scroll-target" [id]="'pp-' + post.id"></div>
            </div>
            <div class="mbm-post block">
              <div class="mbm-post-header">
                <div class="mbm-post-time">{{ post.date | formatDate: 'h-m' }}</div>
                <div class="mbm-post-date">{{ post.date | dfnsFormat: 'yyyy. LLLL dd.' }}</div>
                <app-social-share
                  class="mbm-post-share"
                  [emailSubject]="article()?.title || ''"
                  [isAnchorLink]="true"
                  [linkToCopy]="getMinuteByMinuteLink(post.id)"
                ></app-social-share>
              </div>
              <h2 class="mbm-post-title">{{ post.title }}</h2>

              @for (element of post.body; track element.id) {
                @switch (element.type) {
                  @case (ArticleBodyType.Wysywyg) {
                    @for (wysiwygDetail of element?.details; track wysiwygDetail.key) {
                      <app-wysiwyg-box class="mbm-wysiwyg" [html]="wysiwygDetail?.value" trArticleFileLink [useTableWrapper]="true"></app-wysiwyg-box>
                    }
                  }
                }
              }
            </div>
          }
        }

        @for (element of article()?.body; track element.id) {
          @switch (element.type) {
            @case (ArticleBodyType.Wysywyg) {
              @for (wysiwygDetail of element?.details; track wysiwygDetail) {
                <app-wysiwyg-box [html]="wysiwygDetail?.value | toLinkify: articleMeta()" trArticleFileLink [useTableWrapper]="true"></app-wysiwyg-box>
              }
            }
            @case (ArticleBodyType.Advert) {
              @if (interrupter?.mobile?.[$any(element).adverts.mobile]; as ad) {
                <kesma-advertisement-adocean [isExceptionAdvertEnabled]="isExceptionAdvertEnabled" [ad]="ad"></kesma-advertisement-adocean>
              }
              @if (interrupter?.desktop?.[$any(element).adverts.desktop]; as ad) {
                <kesma-advertisement-adocean [isExceptionAdvertEnabled]="isExceptionAdvertEnabled" [ad]="ad"></kesma-advertisement-adocean>
              }
            }
            @case (ArticleBodyType.Voting) {
              @if (voteCache[getId(element)] | async; as voteData) {
                <app-voting
                  class="block"
                  (vote)="onVotingSubmit($event, voteData)"
                  [data]="voteData.data"
                  [voteId]="voteData.votedId"
                  [styleId]="VotingType.ARTICLE"
                />
              }
            }
            @case (ArticleBodyType.SubsequentDossier) {
              <app-dossier-card
                class="block"
                [styleId]="DossierCardType.Article"
                [data]="backendBodyDossierToDossierData(element.details[0])"
              ></app-dossier-card>
            }
            @case (ArticleBodyType.Quiz) {
              <app-quiz [data]="element?.details?.[0]?.value" [showMoreQuizButton]="true" />
            }
            @case (ArticleBodyType.MediaVideo) {
              <kesma-article-video class="block" [data]="element?.details[0]?.value"></kesma-article-video>
            }
            @case (ArticleBodyType.Gallery) {
              @if (galleries()[element?.details[0]?.value?.id]) {
                <app-bors-slider-gallery
                  (fullscreenLayerClicked)="openGalleryDedicatedRouteLayer($event)"
                  (slideChanged)="handleGallerySlideChange(galleries()[element?.details[0]?.value?.id], $event)"
                  [data]="galleries()[element?.details[0]?.value?.id]"
                  [highlightedImageId]="element?.details[1]?.value?.id"
                  [isInsideAdultArticleBody]="article()?.isAdultsOnly"
                ></app-bors-slider-gallery>
              }
            }
            @case (ArticleBodyType.TenArticleRecommender) {
              @if (backendTenArticleRecommenderToData(element); as data) {
                <div class="ten-recommender block">
                  <app-ten-article-recommender [articles]="data.articles" [withImage]="data.withImage"></app-ten-article-recommender>
                </div>
              }
            }
            @case (ArticleBodyType.Article) {
              <app-article-card
                class="block"
                [styleId]="ArticleCardType.ArticleRecommendationSideImg"
                [data]="backendBodyArticleToArticleCard(element.details[0])"
              />
            }
            @case (ArticleBodyType.Infobox) {
              <app-information-box [informationBoxDetails]="element.details"></app-information-box>
            }
            @case (ArticleBodyType.AnchorToComments) {
              <app-anchor-to-comments></app-anchor-to-comments>
            }
            @case (ArticleBodyType.PrivateOpinion) {
              <app-private-opinion></app-private-opinion>
            }
          }
        }
        @if (article()) {
          <div #dataTrigger></div>
        }

        @if (sponsoredTag()) {
          <app-sponsored-tag-box [sponsoredTag]="sponsoredTag()" [excludedSlug]="article()?.slug || ''" />
        }

        <app-external-recommendations
          [roadblock_ottboxextra]="adverts?.desktop?.['roadblock_ottboxextra']"
          [mobilrectangle_ottboxextra]="adverts?.mobile?.['mobilrectangle_ottboxextra']"
          [isExceptionAdvertEnabled]="isExceptionAdvertEnabled"
        ></app-external-recommendations>

        @if (adverts?.desktop?.roadblock_2; as ad) {
          <kesma-advertisement-adocean [style]="{ margin: '32px 0px' }" [isExceptionAdvertEnabled]="isExceptionAdvertEnabled" [ad]="ad"></kesma-advertisement-adocean>
        }
        @if (adverts?.mobile?.mobilrectangle_2; as ad) {
          <kesma-advertisement-adocean [style]="{ margin: '32px 0px' }" [isExceptionAdvertEnabled]="isExceptionAdvertEnabled" [ad]="ad"></kesma-advertisement-adocean>
        }

        <app-google-news class="block" />

        @if (articleSocials() && !articleSocials()?.disableComments) {
          <div class="scroll-target-wrapper">
            <div class="scroll-target" id="comment-section"></div>
          </div>
          <app-comment-section [articleId]="article().id" [commentCount]="articleSocials()?.commentCount ?? 0" />
        }
      </div>
      <aside>
        <app-sidebar
          [articleId]="article()?.id"
          [articleSlug]="article()?.slug"
          [categorySlug]="article()?.primaryColumn?.slug"
          [adPageType]="adPageType()"
        ></app-sidebar>
      </aside>
    </div>
  </section>
}
