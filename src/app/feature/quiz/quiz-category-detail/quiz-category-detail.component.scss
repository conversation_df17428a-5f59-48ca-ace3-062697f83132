@use 'shared' as *;

:host {
  display: block;
  margin-block: 32px;
  @include media-breakpoint-down(md) {
    padding-inline: 16px;
  }
  ::ng-deep {
    app-page-title h2.page-title {
      color: $dark-blue-800;
      margin-block: 32px;
    }
  }
  .left-column {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
  .quiz-list {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 32px;
    @include media-breakpoint-down(lg) {
      grid-template-columns: 1fr 1fr;
      gap: 16px;
    }
  }
  .list-separator {
    margin: 32px 0;
    border-color: var(--kui-gray-300);
    opacity: 0.3;
    @include media-breakpoint-down(md) {
      margin: 16px 0;
    }
  }
  .with-aside {
    gap: 24px;
  }
  .ad {
    grid-column: 1 / -1;
  }
}
