<section>
  <div class="wrapper">
    <app-breadcrumb [data]="breadcrumbItems()"></app-breadcrumb>
    <app-block-title-row [data]="{ text: quizCategoryTitle() }"></app-block-title-row>
  </div>
  <hr class="list-separator" />
  <div class="wrapper with-aside">
    <div class="left-column">
      <div class="quiz-list">
        @for (quiz of quizList(); track quiz.quizId; let index = $index) {
          <app-quiz-mini-card [title]="quiz.quizTitle" [link]="['/kvizek', quiz.quizSlug]"></app-quiz-mini-card>
          @if (index === 24) {
            <!-- TODO: AD -->
          }
        }
      </div>
      @if (quizMeta()?.limitable; as limitable) {
        @if (limitable?.pageMax) {
          <app-pager
            [rowAllCount]="limitable?.rowAllCount"
            [rowOnPageCount]="limitable?.rowOnPageCount"
            [isListPager]="true"
            [hasSkipButton]="true"
            [allowAutoScrollToTop]="true"
            [maxDisplayedPages]="5"
          ></app-pager>
        }
      }
    </div>
    <aside>
      <app-sidebar />
    </aside>
  </div>
</section>
