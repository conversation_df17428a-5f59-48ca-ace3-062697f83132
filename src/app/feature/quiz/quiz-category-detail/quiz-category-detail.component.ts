import { ChangeDetectionStrategy, Component, computed, effect, inject } from '@angular/core';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { ActivatedRoute } from '@angular/router';
import { SchemaOrgService, SeoService } from '@trendency/kesma-core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ApiResponseMetaList, ApiResult, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';
import { Quiz } from '../quiz.definitions';
import { map } from 'rxjs/operators';
import { BlockTitleRowComponent, BreadcrumbComponent, createBorsOnlineTitle, defaultMetaInfo, makeBreadcrumbSchema, PagerComponent } from '../../../shared';
import { QuizMiniCardComponent } from '../../../shared/components/quiz-mini-card/quiz-mini-card.component';

@Component({
  selector: 'app-quiz-category-detail',
  templateUrl: './quiz-category-detail.component.html',
  styleUrl: './quiz-category-detail.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SidebarComponent, BlockTitleRowComponent, PagerComponent, QuizMiniCardComponent, BreadcrumbComponent],
})
export class QuizCategoryDetailComponent {
  private readonly route = inject(ActivatedRoute);
  private readonly seoService = inject(SeoService);
  private readonly schemaService = inject(SchemaOrgService);

  readonly breadcrumbItems = computed(() => {
    const breadcrumbBase = [
      {
        label: 'Aktuális',
        url: ['/rovat/aktualis'],
      },
      {
        label: 'Kvíz',
        url: ['/rovat/aktualis/kviz'],
      },
    ];
    if (!this.quizCategoryTitle() || !this.quizList()?.[0]?.quizCategorySlug) {
      return breadcrumbBase;
    }
    return [
      ...breadcrumbBase,
      {
        label: this.quizCategoryTitle(),
        url: ['/rovat/aktualis/kviz', this.quizList()?.[0]?.quizCategorySlug ?? ''],
      },
    ];
  });

  readonly routeData = toSignal<ApiResult<Quiz[], ApiResponseMetaList>>(
    this.route.data.pipe(
      map(({ data: routeResult }) => {
        const { data } = routeResult as ApiResult<Quiz[], ApiResponseMetaList>;
        const categoryTitle = data?.[0]?.quizCategoryTitle;
        this.setMetaData(categoryTitle);
        return routeResult;
      })
    )
  );
  constructor() {
    effect(() => {
      const breadcrumbSchema = makeBreadcrumbSchema(this.breadcrumbItems());
      this.schemaService.insertSchema(breadcrumbSchema);
    });
  }

  readonly quizList = computed<Quiz[] | undefined>(() => this.routeData()?.data);
  readonly quizMeta = computed<ApiResponseMetaList | undefined>(() => this.routeData()?.meta);

  readonly quizCategoryTitle = computed<string>(() => {
    return this.quizList()?.[0]?.quizCategoryTitle ?? '';
  });

  private setMetaData(categoryTitle: string): void {
    const canonical = createCanonicalUrlForPageablePage('rovat/aktualis/kviz', this.route.snapshot);
    canonical && this.seoService.updateCanonicalUrl(canonical);
    const title = createBorsOnlineTitle(`${categoryTitle} kvízek`);
    this.seoService.setMetaData({
      ...defaultMetaInfo,
      title,
      ogTitle: title,
    });
  }
}
