<section>
  <div class="wrapper">
    <app-breadcrumb [data]="breadcrumbItems"></app-breadcrumb>
    <app-block-title-row [data]="{ text: 'Kvíz' }"></app-block-title-row>
    <hr class="list-separator" />
  </div>
  <div class="wrapper with-aside">
    <div class="left-column">
      @for (category of quizCategories(); track category.id; let index = $index) {
        <div class="category">
          <a class="category-link" [routerLink]="['/rovat/aktualis/kviz', category.slug]">
            <h2 class="category-title">{{ category.title }}</h2>
          </a>
          @if (category.quizzes?.length) {
            <div class="category-quizzes">
              @for (quiz of category.quizzes; track quiz.quizId) {
                <app-quiz-mini-card [title]="quiz.quizTitle" [link]="['/kvizek', quiz.quizSlug]"></app-quiz-mini-card>
              }
            </div>
          }
          <a [routerLink]="['/rovat/aktualis/kviz', category.slug]" class="category-link">
            Még {{ category.title }} kvíz
            <kesma-icon class="icon" name="arrow-right-long" [size]="16"></kesma-icon>
          </a>
          @if (!$last) {
            <hr class="list-separator" />
          }
        </div>
        @if (index === 0 || index === 3) {
          <kesma-advertisement-adocean></kesma-advertisement-adocean>
        }
      }
      @if (quizMeta()?.limitable; as limitable) {
        @if (limitable?.pageMax) {
          <app-pager
            [rowAllCount]="limitable?.rowAllCount"
            [rowOnPageCount]="limitable?.rowOnPageCount"
            [isListPager]="true"
            [hasSkipButton]="true"
            [allowAutoScrollToTop]="true"
            [maxDisplayedPages]="5"
          ></app-pager>
        }
      }
    </div>
    <aside>
      <app-sidebar />
    </aside>
  </div>
</section>
