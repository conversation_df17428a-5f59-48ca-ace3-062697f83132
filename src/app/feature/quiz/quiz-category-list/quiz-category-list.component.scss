@use 'shared' as *;

:host {
  display: block;
  margin-block: 32px;
  @include media-breakpoint-down(md) {
    padding-inline: 16px;
  }
  ::ng-deep {
    app-page-title h2.page-title {
      color: $dark-blue-800;
      margin-block: 32px;
    }
  }
  .left-column {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
  .category {
    display: flex;
    flex-direction: column;
    &-link {
      color: var(--kui-slate-950);
    }
    &-title {
      color: $dark-blue-800;
      font-size: 48px;
      font-style: normal;
      font-weight: 800;
      line-height: 42px; /* 87.5% */
      text-transform: uppercase;
      overflow-wrap: anywhere;
      margin-bottom: 32px;
      @include media-breakpoint-down(md) {
        margin-bottom: 16px;
        font-size: 28px;
        line-height: 28px; /* 100% */
      }
    }
    &-quizzes {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr;
      gap: 16px;
      @include media-breakpoint-down(lg) {
        grid-template-columns: 1fr 1fr;
        gap: 16px;
      }
    }
    &-link {
      margin-top: 32px;
      display: flex;
      color: $dark-blue-800;
      gap: 10px;
      font-size: 16px;
      font-weight: 700;
      line-height: 20px; /* 125% */
      @include media-breakpoint-down(md) {
        margin-top: 16px;
      }
    }
  }
  .list-separator {
    margin: 32px 0;
    border-color: var(--kui-gray-300);
    opacity: 0.3;
    @include media-breakpoint-down(md) {
      margin: 16px 0;
    }
  }
  .with-aside {
    gap: 24px;
  }
}
