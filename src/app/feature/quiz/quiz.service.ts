import { inject, Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { forkJoin, Observable } from 'rxjs';
import { ApiResult, Quiz as KesmaQuiz } from '@trendency/kesma-ui';
import { Quiz, QuizCategory, QuizCategoryWithChildQuizzes } from './quiz.definitions';
import { map, switchMap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class QuizService {
  private readonly reqService = inject(ReqService);

  getQuizCategoryList(params: object): Observable<ApiResult<QuizCategory[]>> {
    return this.reqService.get<ApiResult<QuizCategory[]>>('content-group/source/quiz/quiz-category/list', {
      params,
    });
  }

  getQuizCategoryListWithChildQuizzes(params: object): Observable<ApiResult<QuizCategoryWithChildQuizzes[]>> {
    return this.reqService
      .get<ApiResult<QuizCategory[]>>('content-group/source/quiz/quiz-category/list', {
        params,
      })
      .pipe(
        switchMap((res) => {
          const categories = res.data;
          const categorySlugs = categories.map((category) => category.slug);
          const quizes$ = forkJoin(categorySlugs.map((categorySlug) => this.getQuizListByCategorySlug({ rowCount_limit: '4' }, categorySlug)));
          return quizes$.pipe(
            map((quizes) => {
              const cats = categories.map((category, index) => {
                return {
                  ...category,
                  quizzes: quizes[index]?.data,
                } as QuizCategoryWithChildQuizzes;
              });
              return {
                ...res,
                data: cats,
              };
            })
          );
        })
      );
  }

  getQuizListByCategorySlug(params: object, categorySlug: string): Observable<ApiResult<Quiz[]>> {
    return this.reqService.get<ApiResult<Quiz[]>>('content-group/quiz/quiz-category/list', {
      params: {
        ...params,
        quizCategorySlug_filter: categorySlug,
      },
    });
  }

  getQuizBySlug(quizSlug: string): Observable<ApiResult<KesmaQuiz>> {
    return this.reqService.get<ApiResult<KesmaQuiz>>(`content-group/quiz/${quizSlug}`, {});
  }
}
