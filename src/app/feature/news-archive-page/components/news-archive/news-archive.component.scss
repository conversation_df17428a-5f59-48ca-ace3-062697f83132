@use 'shared' as *;

$grey: #ddd;

:host {
  display: block;
  margin: 48px 0;

  .content-wrapper {
    margin: 0 auto;
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0.5px solid $grey;
    width: 1160px;
    @include media-breakpoint-down(md) {
      max-width: calc(100% - 40px) !important;
    }
  }

  .archive-title {
    color: var(--kui-red-500);
    font-size: 30px;
    padding: 20px;
    border-bottom: 0.5px solid $grey;

    @include media-breakpoint-down(sm) {
      text-align: center;
    }
  }

  .years-container {
    border-right: 0.5px solid $grey;
    width: fit-content;
  }

  .months-container {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 20px;
    padding: 20px;
    align-items: center;

    @include media-breakpoint-down(sm) {
      flex-direction: column;
    }

    &:not(:last-child) {
      border-bottom: 0.5px solid $grey;
    }
  }

  .year {
    &-title {
      font-size: 28px;
      font-weight: 700;
      width: 70px;
    }
  }

  .months {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: flex-start;
    align-items: center;
    padding-left: 5px;

    a {
      color: var(--kui-red-500);
      font-size: 14px;
      font-weight: 700;
      text-transform: uppercase;

      &:not(:last-child) {
        &::after {
          background-color: #bbb;
          border-radius: 50%;
          content: '';
          display: inline-block;
          height: 4px;
          margin-left: 8px;
          vertical-align: middle;
          width: 4px;
        }
      }
    }
  }
}
