@use 'shared' as *;
@use 'sass:color';

@mixin block-margin() {
  margin-bottom: 32px;

  @include media-breakpoint-down(md) {
    margin-bottom: 16px;
  }
}

@mixin highlight-text-style() {
  box-decoration-break: clone;
  display: inline;
  font-size: 20px;
  font-weight: bold;
  line-height: 28px;
  margin: 10px 0;
  background-color: var(--kui-white);

  @include media-breakpoint-down(md) {
    font-size: 18px;
    line-height: 26px;
  }

  // Inline paragraphs don't wrap after each other
  // This element enforces a line-break after each paragraph, except the last one
  &:not(:last-child)::after {
    content: '\a';
    white-space: pre;
  }
}

/* .clearfix {
  @include clearfix();
}
 */
.block-content::ng-deep {
  @include block-margin;

  p,
  li,
  span {
    //font-family: $font-family;
    font-size: 20px;
    line-height: 26px;

    @include media-breakpoint-down(sm) {
      font-size: 18px;
      line-height: 24px;
    }
  }

  h2 {
    font-size: 24px;
    line-height: 29px;
    font-weight: bold;
    margin-bottom: 32px;

    @include media-breakpoint-down(sm) {
      margin-bottom: 16px;
      font-size: 20px;
      line-height: 24px;
    }
  }

  p:not(td p),
  p {
    @include block-margin;
  }

  a {
    color: var(--kui-red-500);
    font-weight: bold;
    text-decoration: underline;
    transition: all 0.3s ease-in-out;

    &:hover {
      text-decoration: none;
      color: var(--kui-red-550);
    }
  }

  strong,
  b {
    font-weight: bold;
  }

  i,
  em {
    font-style: italic;
  }

  figure.image {
    display: table;
    clear: both;
    text-align: center;
    margin-inline: auto;

    @include block-margin;

    &.image-style-align-left {
      float: left;
      margin-right: 20px;
      margin-bottom: unset;
      margin-top: 0;

      @include media-breakpoint-down(sm) {
        margin-right: 10px;
      }
    }

    &.image-style-align-right {
      float: right;
      margin-left: 20px;
      margin-bottom: unset;
      margin-top: 0;

      @include media-breakpoint-down(sm) {
        margin-left: 10px;
      }
    }

    figcaption {
      text-align: left;
      padding: 16px 0;
      position: relative;
      font-size: 14px;
      font-style: normal;
      line-height: 20px;
      border-bottom: 1px solid var(--kui-gray-200);

      &::before {
        display: none;
      }

      @include media-breakpoint-down(sm) {
        font-size: 14px;
        line-height: 16px;
      }
    }
  }

  ul {
    margin: 32px 0;
    list-style-type: circle;

    @include media-breakpoint-down(md) {
      margin: 16px 0;
    }

    li {
      list-style: none;
      margin-bottom: 4px;
      padding-left: 18px;
      line-height: 26px;
      font-size: 20px;
      margin-top: 0;

      &::before {
        width: 10px;
        height: 10px;
        background: var(--kui-red-500);
        margin-top: 8px;
        content: ' ';
        display: inline-block;
        border-radius: 50%;
        position: absolute;
        left: 0;
      }

      @include media-breakpoint-down(sm) {
        font-size: 18px;
        line-height: 24px;
      }
    }
  }

  ol {
    padding-left: 0;
    margin: 32px 0;

    @include media-breakpoint-down(md) {
      margin: 16px 0;
    }

    ::marker {
      color: var(--kui-red-500);
      font-size: 20px;
      font-style: normal;
      font-weight: 700;
      line-height: 26px;
    }

    li {
      margin-left: 15px;
      position: relative;
      margin-bottom: 4px;
      font-size: 20px;
      line-height: 26px;
      padding-left: 8px;
      list-style-type: decimal;
      margin-top: 0;

      @include media-breakpoint-down(sm) {
        font-size: 18px;
        line-height: 24px;
      }
    }
  }

  .table-scroller {
    width: 100%;
    overflow: auto;
  }

  figure.table {
    display: table;
    margin: auto;
  }

  table {
    max-width: 100%;
    border: 0;
    overflow-wrap: initial;

    @include block-margin;

    thead {
      tr {
        td,
        th {
          //background: color.adjust(var(--kui-red-500), $lightness: -10%);
          color: var(--kui-white);
          padding: 9px 16px;
          font-size: 12px;
        }

        th {
          text-transform: uppercase;
        }

        @include media-breakpoint-down(md) {
          padding: 5px 16px;
        }
      }
    }

    tr {
      td,
      th {
        background: var(--kui-red-500);
        color: var(--kui-white);
        border: 0;
        font-size: 16px;
        line-height: 30px;
        padding: 22px 16px;
        border-left: 2px solid var(--kui-white);
        border-top: 2px solid var(--kui-white);

        @include media-breakpoint-down(md) {
          padding: 10px 16px;
        }

        &:first-child {
          padding-left: 33px;
        }

        &:last-child {
          padding-right: 33px;
        }
      }

      th {
        font-weight: 500;
      }
    }

    td:last-of-type,
    th:last-of-type {
      border-right: 2px solid var(--kui-white);
    }

    tr:last-of-type {
      > td,
      > th {
        border-bottom: 2px solid var(--kui-white);
      }
    }
  }

  .table-wrapper {
    @include media-breakpoint-down(sm) {
      overflow-x: auto;
      table {
        display: block;
      }
    }
    &:not(.table-style-align-left, .table-style-align-right) {
      overflow-x: auto;
      table {
        display: block;
      }
    }
  }

  blockquote {
    margin: 32px 0;
    position: relative;

    @include media-breakpoint-down(sm) {
      margin: 16px 0;
      padding: 20px 0 5px 20px;
    }

    p {
      &:last-child {
        margin-bottom: 0;
      }
    }

    &.quote {
      padding: 15px 0 15px 30px;
      border-left: 10px solid var(--kui-red-500);

      &,
      p {
        position: relative;
        z-index: 1;
        font-size: 48px;
        letter-spacing: 0;
        line-height: 52px;

        @include media-breakpoint-down(sm) {
          font-size: 32px;
          line-height: 36px;
        }
      }
    }

    &.highlight {
      padding: 15px 30px;
      background: var(--kui-red-500);
      color: var(--kui-white);

      &,
      p {
        position: relative;
        z-index: 1;
        font-size: 48px;
        letter-spacing: 0;
        line-height: 52px;

        @include media-breakpoint-down(sm) {
          font-size: 32px;
          line-height: 36px;
        }
      }

      a,
      p a {
        color: var(--kui-red-500);
      }
    }

    &.border-text {
      border-top: 6px solid var(--kui-red-500);
      background: rgb(226 0 59 / 10%);
      padding: 30px;
    }
  }

  /* Fallback for old highlight markups which use a simple span */
  p > span.custom-text-style.highlight {
    padding: 0;
    background-color: var(--kui-white);
    @include highlight-text-style();
  }

  .custom-text-style {
    display: block;
    margin: 32px 0;
    position: relative;
    clear: both;

    &:last-child {
      margin-bottom: 0;
    }

    @include media-breakpoint-down(sm) {
      margin: 16px 0;
      padding: 20px 0 5px 20px;
    }

    &.quote {
      padding: 40px 0 0 32px;
      border-left: 3px solid var(--kui-red-500);
      position: relative;
      z-index: 1;
      letter-spacing: 0;

      p {
        font-size: 20px;
        font-style: normal;
        font-weight: 700;
        line-height: 28px;
        margin-bottom: 0;

        &:last-child:after {
          content: none;
        }

        &:first-child:before {
          content: none;
        }
      }

      @include media-breakpoint-down(sm) {
        padding: 40px 0 0 16px;

        p {
          font-size: 18px;
        }
      }

      &.quote-logo {
        &::before {
          content: ' ';
          width: 32px;
          height: 32px;
          display: block;
          position: absolute;
          top: 0;
          left: 32;

          @include icon('icons/quote.svg');
        }
      }
    }

    &.highlight {
      padding: 32px;
      background-color: var(--kui-gray-250);
      color: var(--kui-black-950);
      margin: 0;
      @include block-margin;

      p:not(strong) {
        font-size: 20px;
        font-weight: 400;
        line-height: 26px;
      }

      p > strong {
        display: inline-block;
        font-size: 22px;
        font-weight: 700;
        line-height: 24px;
        margin-bottom: 12px;
      }

      @include media-breakpoint-down(sm) {
        padding: 16px;
        p:not(strong) {
          font-size: 18px;
          line-height: 24px;
        }
      }
    }

    &.highlight-style2 {
      border-left: 3px solid var(--kui-red-500);
      padding: 0;
      padding-left: 32px;

      p {
        font-size: 20px;
        font-weight: 700;
        line-height: 28px;
      }

      @include media-breakpoint-down(sm) {
        padding-left: 16px;

        p {
          font-size: 18px;
          line-height: 26px;
        }
      }
    }

    &.border-text {
      border-top: 0;
      background: var(--kui-gray-250);
      padding: 32px;

      h2 {
        font-size: 22px;
        font-style: normal;
        font-weight: 700;
        line-height: 24px;
        margin-bottom: 12px;
      }

      p {
        font-size: 20px;
        font-style: normal;
        font-weight: 400;
        line-height: 26px;
        margin-bottom: 0;
      }

      @include media-breakpoint-down(sm) {
        h2 {
          font-size: 20px;
        }

        p {
          font-size: 18px;
          line-height: 24px;
        }
      }
    }

    &.underlined-text {
      display: inline;
      font-size: inherit;
      word-break: break-word;
      box-shadow: 0 2px 0 0 var(--kui-red-500);
      text-decoration: none;
      padding: 0;
    }
  }

  .raw-html-embed {
    width: 100%;
    display: block;

    @include block-margin;

    // Do not use flex here, because some 3rd party stuff (iframe.ly) doesn't like it

    > * {
      margin: 0 auto;
    }

    iframe {
      max-width: 100%;
      display: flex;
      justify-content: center;
    }
  }

  .media {
    .embedly-card {
      margin-bottom: 40px;
    }
  }
}
