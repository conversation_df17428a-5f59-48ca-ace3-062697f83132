<app-page-title title="KVÍZ" />

@if (currentQuestionIndex !== data?.questions?.length) {
  <img class="quiz-question-image" [src]="currentQuestion?.image || PlaceholderImgWithBg" alt="Kvíz kérd<PERSON> á<PERSON>r<PERSON> kép" loading="lazy" />
} @else if (rating && currentQuestionIndex === data?.questions?.length) {
  <img class="quiz-question-image" [src]="rating?.image || PlaceholderImgWithBg" loading="lazy" alt="" />
}

<div class="quiz-question">
  <span class="quiz-stepper">
    {{ rating && currentQuestionIndex === data?.questions?.length ? 'Eredmény' : currentQuestionIndex + 1 + ' / ' + data?.questions?.length }}
  </span>
  <h5 class="quiz-question-text">
    @if (rating && currentQuestionIndex === data?.questions?.length) {
      @if (isShowOnlyLoggedUsers && !currentUser()) {
        A kvíz eredménye csak bejelentkezett felhasználók számára érhető el. Kérjük, jelentkezz be a megtekintéshez!
      } @else {
        {{ rating.text }}
      }
    } @else {
      {{ currentQuestion?.title }}
    }
  </h5>
</div>

@if (currentQuestionIndex !== data?.questions?.length) {
  <div class="answer-list">
    @for (answer of currentQuestion?.answers; track answer.id; let answerIndex = $index) {
      <div
        #listItem
        class="answer-list-item"
        [class.wrong]="!answer.isCorrect && givenAnswers[currentQuestionIndex] !== undefined && givenAnswers[currentQuestionIndex] === answerIndex"
        [class.correct]="answer.isCorrect && givenAnswers[currentQuestionIndex] !== undefined && givenAnswers[currentQuestionIndex] === answerIndex"
        [class.correct-answer]="answer.isCorrect && givenAnswers[currentQuestionIndex] !== undefined && givenAnswers[currentQuestionIndex] !== answerIndex"
        [class.disabled]="givenAnswers[currentQuestionIndex] !== undefined"
        (click)="
          givenAnswers[currentQuestionIndex] !== undefined ? $event.stopPropagation() : onSelectAnswer(currentQuestionIndex, answerIndex); onPickAnswer(answer)
        "
      >
        @if (listItem.classList.contains('wrong')) {
          <kesma-icon class="wrong" name="close-circle" [size]="20" />
        } @else if (listItem.classList.contains('correct') || listItem.classList.contains('correct-answer')) {
          <kesma-icon class="correct" name="checked-circle" [size]="20" />
        } @else {
          <kesma-icon class="circle" name="circle" [size]="20" />
        }
        <label
          class="radio-label"
          [for]="'answer_' + currentQuestion?.id + '_' + answerIndex"
          [class.hidden-original-circle]="listItem.classList.contains('correct') || listItem.classList.contains('wrong')"
        >
          {{ answer.title }}
        </label>
        <label class="result-label">
          {{ answer.isCorrect ? 'Helyes válasz' : 'Helytelen válasz' }}
        </label>
      </div>
    }
  </div>
}

<div class="quiz-footer">
  @if (isNextButtonVisible && currentQuestionIndex !== data?.questions?.length) {
    <app-simple-button class="w-100" color="secondary" [disabled]="!selectedAnswer" (click)="onGetNextQuestion()">
      {{ currentQuestionIndex! + 1 < data?.questions?.length! ? 'Következő kérdés' : 'Tovább az eredményre' }}
    </app-simple-button>
  }
  @if (rating && currentQuestionIndex === data?.questions?.length) {
    <div class="quiz-result-share">
      @if (isShowOnlyLoggedUsers && !currentUser()) {
        <a class="share-link" (click)="onLogin()">Bejelentkezés</a>
      } @else {
        <a class="share-link" (click)="onFBShareClick()">Facebook megosztás</a>
      }
    </div>
  }
  @if (showMoreQuizButton()) {
    <app-simple-button class="w-100 more-quiz-button" color="secondary" [routerLink]="['/rovat/aktualis/kviz']"> További kvízek </app-simple-button>
  }
</div>
